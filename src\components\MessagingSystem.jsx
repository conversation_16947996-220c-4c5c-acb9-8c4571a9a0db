import React, { useState, useEffect, useRef } from 'react';
import { useAuth } from '../AuthContext';
import {
  sendMessage,
  getMessages,
  getUserConversations,
  markMessagesAsRead,
  deleteMessage,
  editMessage,
  searchMessages,
  getUnreadMessageCount,
  toggleBlockUser,
  isUserBlocked
} from '../services/messaging';

/**
 * Private Messaging System Component
 * Provides secure messaging with real-time updates and user status
 */
export default function MessagingSystem({ onClose }) {
  const { currentUser } = useAuth();
  const [conversations, setConversations] = useState([]);
  const [selectedConversation, setSelectedConversation] = useState(null);
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [onlineUsers, setOnlineUsers] = useState(new Set());
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [editingMessage, setEditingMessage] = useState(null);
  const [showUserSearch, setShowUserSearch] = useState(false);
  const messagesEndRef = useRef(null);

  useEffect(() => {
    if (!currentUser) return;

    let unsubscribeConversations = () => {};
    let unsubscribeUnread = () => {};

    // Load conversations with error handling
    try {
      unsubscribeConversations = getUserConversations(currentUser.uid, (convs) => {
        setConversations(convs);
        setLoading(false);
      });
    } catch (error) {
      console.error('Error setting up conversations listener:', error);
      setConversations([]);
      setLoading(false);
    }

    // Load unread count with error handling
    try {
      unsubscribeUnread = getUnreadMessageCount(currentUser.uid, setUnreadCount);
    } catch (error) {
      console.error('Error setting up unread count listener:', error);
      setUnreadCount(0);
    }

    // Simulate online status (in production, use real presence system)
    const simulateOnlineUsers = () => {
      const mockOnlineUsers = new Set(['user1', 'user2', 'user3']); // Mock data
      setOnlineUsers(mockOnlineUsers);
    };
    simulateOnlineUsers();
    const onlineInterval = setInterval(simulateOnlineUsers, 30000);

    // Set a timeout to ensure loading state doesn't persist indefinitely
    const loadingTimeout = setTimeout(() => {
      if (loading) {
        setLoading(false);
        setConversations([]);
      }
    }, 5000); // 5 second timeout

    return () => {
      unsubscribeConversations();
      unsubscribeUnread();
      clearInterval(onlineInterval);
      clearTimeout(loadingTimeout);
    };
  }, [currentUser]);

  useEffect(() => {
    if (selectedConversation) {
      // Load messages for selected conversation
      const unsubscribeMessages = getMessages(selectedConversation.id, (msgs) => {
        setMessages(msgs);
        // Mark messages as read
        markMessagesAsRead(selectedConversation.id, currentUser.uid);
      });

      return () => unsubscribeMessages();
    }
  }, [selectedConversation, currentUser]);

  useEffect(() => {
    // Auto-scroll to bottom when new messages arrive
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = async () => {
    if (!newMessage.trim() || !selectedConversation || sending) return;

    try {
      setSending(true);
      const otherParticipant = selectedConversation.participants.find(
        p => p !== currentUser.uid
      );

      await sendMessage(currentUser.uid, otherParticipant, newMessage);
      setNewMessage('');
    } catch (error) {
      console.error('Error sending message:', error);
      alert('Failed to send message. Please try again.');
    } finally {
      setSending(false);
    }
  };

  const handleEditMessage = async (messageId, newContent) => {
    try {
      await editMessage(messageId, currentUser.uid, newContent);
      setEditingMessage(null);
    } catch (error) {
      console.error('Error editing message:', error);
      alert('Failed to edit message: ' + error.message);
    }
  };

  const handleDeleteMessage = async (messageId) => {
    if (!confirm('Are you sure you want to delete this message?')) return;

    try {
      await deleteMessage(messageId, currentUser.uid);
    } catch (error) {
      console.error('Error deleting message:', error);
      alert('Failed to delete message: ' + error.message);
    }
  };

  const handleSearch = async () => {
    if (!searchTerm.trim()) {
      setSearchResults([]);
      return;
    }

    try {
      const results = await searchMessages(currentUser.uid, searchTerm);
      setSearchResults(results);
    } catch (error) {
      console.error('Error searching messages:', error);
    }
  };

  const formatTime = (timestamp) => {
    if (!timestamp) return '';
    const now = new Date();
    const messageTime = new Date(timestamp);
    const diffInHours = (now - messageTime) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return messageTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 168) { // 7 days
      return messageTime.toLocaleDateString([], { weekday: 'short', hour: '2-digit', minute: '2-digit' });
    } else {
      return messageTime.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  const getOtherParticipantId = (conversation) => {
    return conversation.participants.find(p => p !== currentUser.uid);
  };

  const isUserOnline = (userId) => {
    return onlineUsers.has(userId);
  };

  if (loading) {
    return (
      <div className="messaging-system">
        <div className="messaging-loading">
          <div className="loading-spinner"></div>
          <p>Loading messages...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="messaging-system">
      <div className="messaging-content">
        <div className="messaging-header">
          <h2>💬 Messages</h2>
          {unreadCount > 0 && (
            <span className="unread-badge">{unreadCount}</span>
          )}
          <button className="close-messaging-btn" onClick={onClose}>
            ✕
          </button>
        </div>

        <div className="messaging-content-body">
          {/* Conversations Sidebar */}
          <div className="conversations-sidebar">
          <div className="sidebar-header">
            <div className="search-section">
              <input
                type="text"
                placeholder="Search messages..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                className="search-input"
              />
              <button onClick={handleSearch} className="search-btn">
                🔍
              </button>
            </div>
            <button 
              className="new-message-btn"
              onClick={() => setShowUserSearch(true)}
            >
              ✏️ New Message
            </button>
          </div>

          <div className="conversations-list">
            {searchResults.length > 0 ? (
              <div className="search-results">
                <h4>Search Results</h4>
                {searchResults.map(result => (
                  <div key={result.id} className="search-result-item">
                    <div className="result-content">
                      {result.content.substring(0, 100)}...
                    </div>
                    <div className="result-time">
                      {formatTime(result.timestamp)}
                    </div>
                  </div>
                ))}
                <button 
                  className="clear-search-btn"
                  onClick={() => {
                    setSearchTerm('');
                    setSearchResults([]);
                  }}
                >
                  Clear Search
                </button>
              </div>
            ) : (
              conversations.map(conversation => {
                const otherParticipantId = getOtherParticipantId(conversation);
                const isOnline = isUserOnline(otherParticipantId);
                
                return (
                  <div
                    key={conversation.id}
                    className={`conversation-item ${
                      selectedConversation?.id === conversation.id ? 'active' : ''
                    }`}
                    onClick={() => setSelectedConversation(conversation)}
                  >
                    <div className="conversation-avatar">
                      <div className="avatar-circle">
                        {otherParticipantId.charAt(0).toUpperCase()}
                      </div>
                      {isOnline && <div className="online-indicator"></div>}
                    </div>
                    <div className="conversation-info">
                      <div className="conversation-name">
                        User {otherParticipantId.substring(0, 8)}
                      </div>
                      <div className="last-message">
                        {conversation.lastMessage}
                      </div>
                    </div>
                    <div className="conversation-meta">
                      <div className="last-time">
                        {formatTime(conversation.lastMessageTime)}
                      </div>
                    </div>
                  </div>
                );
              })
            )}
          </div>
        </div>

        {/* Messages Area */}
        <div className="messages-area">
          {selectedConversation ? (
            <>
              <div className="messages-header">
                <div className="chat-participant">
                  <div className="participant-avatar">
                    {getOtherParticipantId(selectedConversation).charAt(0).toUpperCase()}
                  </div>
                  <div className="participant-info">
                    <div className="participant-name">
                      User {getOtherParticipantId(selectedConversation).substring(0, 8)}
                    </div>
                    <div className="participant-status">
                      {isUserOnline(getOtherParticipantId(selectedConversation)) 
                        ? '🟢 Online' 
                        : '⚫ Offline'
                      }
                    </div>
                  </div>
                </div>
              </div>

              <div className="messages-list">
                {messages.map(message => (
                  <div
                    key={message.id}
                    className={`message-item ${
                      message.senderId === currentUser.uid ? 'sent' : 'received'
                    } ${message.deleted ? 'deleted' : ''}`}
                  >
                    <div className="message-content">
                      {editingMessage === message.id ? (
                        <div className="edit-message-form">
                          <input
                            type="text"
                            defaultValue={message.content}
                            onKeyPress={(e) => {
                              if (e.key === 'Enter') {
                                handleEditMessage(message.id, e.target.value);
                              }
                            }}
                            onBlur={(e) => handleEditMessage(message.id, e.target.value)}
                            autoFocus
                          />
                        </div>
                      ) : (
                        <div className="message-text">
                          {message.content}
                          {message.edited && (
                            <span className="edited-indicator">(edited)</span>
                          )}
                        </div>
                      )}
                    </div>
                    <div className="message-meta">
                      <span className="message-time">
                        {formatTime(message.timestamp)}
                      </span>
                      {message.senderId === currentUser.uid && !message.deleted && (
                        <div className="message-actions">
                          <button
                            onClick={() => setEditingMessage(message.id)}
                            className="edit-message-btn"
                            title="Edit message"
                          >
                            ✏️
                          </button>
                          <button
                            onClick={() => handleDeleteMessage(message.id)}
                            className="delete-message-btn"
                            title="Delete message"
                          >
                            🗑️
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
                <div ref={messagesEndRef} />
              </div>

              <div className="message-input-area">
                <div className="input-container">
                  <input
                    type="text"
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                    placeholder="Type a message..."
                    className="message-input"
                    disabled={sending}
                  />
                  <button
                    onClick={handleSendMessage}
                    disabled={!newMessage.trim() || sending}
                    className="send-btn"
                  >
                    {sending ? '⏳' : '📤'}
                  </button>
                </div>
              </div>
            </>
          ) : (
            <div className="no-conversation-selected">
              <div className="empty-state">
                <div className="empty-icon">💬</div>
                <h3>Select a conversation</h3>
                <p>Choose a conversation from the sidebar to start messaging</p>
              </div>
            </div>
          )}
        </div>
        </div>
      </div>
    </div>
  );
}
