import React, { useState, useEffect } from 'react';
import { useAuth } from '../AuthContext';
import { useConfirmation } from './ConfirmationDialog';
import {
  autoArchiveCompletedGoals,
  markSupportRequestResolved,
  bulkDeleteItems,
  bulkArchiveItems,
  cleanupOldArchivedItems,
  getCleanupStats
} from '../services/taskCleanup';

/**
 * Task Management Dashboard
 * Provides interface for content cleanup, archiving, and bulk operations
 */
export default function TaskManagementDashboard({ onClose }) {
  const { currentUser } = useAuth();
  const { showConfirmation, ConfirmationComponent } = useConfirmation();
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [lastCleanup, setLastCleanup] = useState(null);

  useEffect(() => {
    if (currentUser) {
      loadStats();
    }
  }, [currentUser]);

  const loadStats = async () => {
    try {
      setLoading(true);
      const cleanupStats = await getCleanupStats(currentUser.uid);
      setStats(cleanupStats);
      
      // Check last cleanup from localStorage
      const lastCleanupDate = localStorage.getItem(`lastCleanup_${currentUser.uid}`);
      if (lastCleanupDate) {
        setLastCleanup(new Date(lastCleanupDate));
      }
    } catch (error) {
      console.error('Error loading cleanup stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAutoArchiveGoals = async () => {
    try {
      setProcessing(true);
      const result = await autoArchiveCompletedGoals(currentUser.uid);
      
      if (result.archivedCount > 0) {
        alert(`Successfully archived ${result.archivedCount} completed goals!`);
        await loadStats(); // Refresh stats
      } else {
        alert('No goals needed archiving at this time.');
      }
    } catch (error) {
      alert('Error archiving goals: ' + error.message);
    } finally {
      setProcessing(false);
    }
  };

  const handleCleanupOldItems = async () => {
    try {
      setProcessing(true);
      const result = await cleanupOldArchivedItems(currentUser.uid);
      
      if (result.cleanupCount > 0) {
        alert(`Successfully cleaned up ${result.cleanupCount} old archived items!`);
        await loadStats(); // Refresh stats
      } else {
        alert('No old items needed cleanup.');
      }
      
      // Update last cleanup date
      const now = new Date();
      localStorage.setItem(`lastCleanup_${currentUser.uid}`, now.toISOString());
      setLastCleanup(now);
    } catch (error) {
      alert('Error cleaning up old items: ' + error.message);
    } finally {
      setProcessing(false);
    }
  };

  const handleAutoArchiveClick = () => {
    if (stats?.completedGoalsEligibleForArchiving > 0) {
      showConfirmation({
        title: 'Auto-Archive Completed Goals',
        message: `This will archive ${stats.completedGoalsEligibleForArchiving} completed goals that have been finished for more than 30 days. Archived goals can still be viewed but won't appear in your active goals list.`,
        confirmText: 'Archive Goals',
        type: 'info',
        onConfirm: handleAutoArchiveGoals
      });
    } else {
      alert('No completed goals are eligible for archiving at this time.');
    }
  };

  const handleCleanupClick = () => {
    if (stats?.oldArchivedItems > 0) {
      showConfirmation({
        title: 'Clean Up Old Archived Items',
        message: `This will permanently delete ${stats.oldArchivedItems} items that have been archived for more than 1 year. This action cannot be undone.`,
        confirmText: 'Clean Up Items',
        type: 'danger',
        requiresTyping: true,
        confirmationPhrase: 'DELETE OLD',
        onConfirm: handleCleanupOldItems
      });
    } else {
      alert('No old archived items need cleanup.');
    }
  };

  const getRecommendations = () => {
    const recommendations = [];
    
    if (stats?.completedGoalsEligibleForArchiving > 0) {
      recommendations.push({
        type: 'archive',
        title: 'Archive Completed Goals',
        description: `You have ${stats.completedGoalsEligibleForArchiving} completed goals that can be archived to clean up your dashboard.`,
        action: handleAutoArchiveClick,
        priority: 'medium'
      });
    }

    if (stats?.oldArchivedItems > 0) {
      recommendations.push({
        type: 'cleanup',
        title: 'Clean Up Old Items',
        description: `You have ${stats.oldArchivedItems} items archived for over a year that can be permanently deleted.`,
        action: handleCleanupClick,
        priority: 'low'
      });
    }

    const daysSinceLastCleanup = lastCleanup 
      ? Math.floor((new Date() - lastCleanup) / (1000 * 60 * 60 * 24))
      : null;

    if (!lastCleanup || daysSinceLastCleanup > 90) {
      recommendations.push({
        type: 'maintenance',
        title: 'Regular Maintenance',
        description: lastCleanup 
          ? `It's been ${daysSinceLastCleanup} days since your last cleanup. Consider running maintenance.`
          : 'Consider running regular maintenance to keep your account organized.',
        action: () => {
          handleAutoArchiveClick();
          setTimeout(handleCleanupClick, 1000);
        },
        priority: 'low'
      });
    }

    return recommendations;
  };

  if (loading) {
    return (
      <div className="task-management-dashboard">
        <div className="dashboard-loading">
          <div className="loading-spinner"></div>
          <p>Loading task management dashboard...</p>
        </div>
      </div>
    );
  }

  const recommendations = getRecommendations();

  return (
    <>
      <div className="task-management-dashboard">
        <div className="dashboard-content">
          <div className="dashboard-header">
            <h2>🧹 Task Management Dashboard</h2>
            <button className="close-dashboard-btn" onClick={onClose}>
              ✕
            </button>
          </div>
          {/* Statistics Overview */}
          <div className="stats-section">
            <h3>📊 Account Overview</h3>
            <div className="stats-grid">
              <div className="stat-card">
                <div className="stat-icon">🎯</div>
                <div className="stat-info">
                  <span className="stat-number">{stats?.completedGoalsEligibleForArchiving || 0}</span>
                  <span className="stat-label">Goals Ready to Archive</span>
                </div>
              </div>
              
              <div className="stat-card">
                <div className="stat-icon">📦</div>
                <div className="stat-info">
                  <span className="stat-number">{stats?.archivedItems || 0}</span>
                  <span className="stat-label">Archived Items</span>
                </div>
              </div>
              
              <div className="stat-card">
                <div className="stat-icon">🗑️</div>
                <div className="stat-info">
                  <span className="stat-number">{stats?.oldArchivedItems || 0}</span>
                  <span className="stat-label">Old Items for Cleanup</span>
                </div>
              </div>
            </div>
          </div>

          {/* Recommendations */}
          {recommendations.length > 0 && (
            <div className="recommendations-section">
              <h3>💡 Recommendations</h3>
              <div className="recommendations-list">
                {recommendations.map((rec, index) => (
                  <div key={index} className={`recommendation-card ${rec.priority}`}>
                    <div className="recommendation-content">
                      <h4>{rec.title}</h4>
                      <p>{rec.description}</p>
                    </div>
                    <button
                      className="recommendation-action"
                      onClick={rec.action}
                      disabled={processing}
                    >
                      {processing ? 'Processing...' : 'Take Action'}
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Manual Actions */}
          <div className="actions-section">
            <h3>🛠️ Manual Actions</h3>
            <div className="actions-grid">
              <div className="action-card">
                <div className="action-icon">🎯</div>
                <div className="action-content">
                  <h4>Archive Completed Goals</h4>
                  <p>Automatically archive financial goals that have been completed for 30+ days</p>
                  <button
                    className="action-btn primary"
                    onClick={handleAutoArchiveClick}
                    disabled={processing || stats?.completedGoalsEligibleForArchiving === 0}
                  >
                    {processing ? 'Processing...' : 'Archive Goals'}
                  </button>
                </div>
              </div>

              <div className="action-card">
                <div className="action-icon">🗑️</div>
                <div className="action-content">
                  <h4>Clean Up Old Items</h4>
                  <p>Permanently delete items that have been archived for over 1 year</p>
                  <button
                    className="action-btn danger"
                    onClick={handleCleanupClick}
                    disabled={processing || stats?.oldArchivedItems === 0}
                  >
                    {processing ? 'Processing...' : 'Clean Up'}
                  </button>
                </div>
              </div>

              <div className="action-card">
                <div className="action-icon">🔄</div>
                <div className="action-content">
                  <h4>Refresh Statistics</h4>
                  <p>Update the dashboard with the latest information</p>
                  <button
                    className="action-btn secondary"
                    onClick={loadStats}
                    disabled={processing}
                  >
                    {processing ? 'Processing...' : 'Refresh'}
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Last Cleanup Info */}
          {lastCleanup && (
            <div className="cleanup-info">
              <p>
                <strong>Last cleanup:</strong> {lastCleanup.toLocaleDateString()} 
                ({Math.floor((new Date() - lastCleanup) / (1000 * 60 * 60 * 24))} days ago)
              </p>
            </div>
          )}
        </div>
      </div>

      <ConfirmationComponent />
    </>
  );
}
